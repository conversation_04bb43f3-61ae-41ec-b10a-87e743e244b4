import React, { useCallback, useEffect, useRef } from 'react'

import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useAtom } from 'jotai'
import { gamePreviewScreenAtomFamily, selectedEditorItem<PERSON>tom } from '@repo/shared/lib/atoms/editor-atoms'
import { ReactGameConfig } from '../types/config' 
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { GameContainer } from '@repo/shared-game-utils/components/GameContainer'
import { GameText } from '@repo/shared-game-utils/components/GameText'
import MainGame, { useGameState } from './GameMain'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { useMusic } from '@repo/shared-game-utils/hooks/useSounds'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { RewardComponent } from '@repo/shared-game-utils/components/RewardComponent'
// import { useRollResult as useRoundReward } from '@repo/shared/features/rewards/hooks/useRoundReward'

export const TryAgainScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)


    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer
                config={config.loseLifeOverlay}
                dataConfigKey="loseLifeOverlay"
                resolveAssetUrl={resolveAssetUrl}
                onClick={(e) => {
                    e.stopPropagation()
                }}
            >
                <GameText
                    config={config.loseLifeTitle}
                    dataConfigKey="loseLifeTitle"
                    className="mb-4"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
                <GameButton
                    config={config.continueButton}
                    dataConfigKey="continueButton"
                    onClick={(e) => {
                        e.stopPropagation()
                        if (onButtonClick) {
                            onButtonClick()
                        }
                    }}
                />
            </GameContainer>
        </div>
    )
}



// Game Over Screen - Shows when player reached the target score
export const GameOverScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer
                config={config.gameOverOverlay}
                dataConfigKey="gameOverOverlay"
                resolveAssetUrl={resolveAssetUrl}
                onClick={(e) => {
                    e.stopPropagation()
                }}
            >
                <GameText
                    config={config.gameOverTitle}
                    dataConfigKey="gameOverTitle"
                    className="mb-4"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
                <GameText
                    config={config.gameOverMessage}
                    dataConfigKey="gameOverMessage"
                    className="mb-4"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
                <GameButton
                    config={config.gameOverContinueButton || config.continueButton}
                    dataConfigKey="gameOverContinueButton"
                    onClick={(e) => {
                        e.stopPropagation()
                        if (onButtonClick) {
                            onButtonClick()
                        }
                    }}
                />
            </GameContainer>
        </div>
    )
}

// Reward Screen - Shows the reward screen when rewards are enabled
export const RewardScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config,  resolveAssetUrl } = useGame<ReactGameConfig>()
    const {roundId} = useGameState()
    const { rewardRollResult } = useRoundReward(roundId)
    useMusic(config.winSound, true, false)

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer
                config={config.rewardOverlay}
                dataConfigKey="rewardOverlay"
                resolveAssetUrl={resolveAssetUrl}
                onClick={(e) => {
                    e.stopPropagation()
                }}
            >
                <GameText
                    config={config.rewardTitle}
                    dataConfigKey="rewardTitle"
                    className="mb-4"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />

                    <div className="mb-4">
                        <RewardComponent dataConfigKey="rewardComponent" reward={rewardRollResult?.result?.reward} />
                    </div>

                {config.gameRewardsHandler?.enableCtaButton !== false && (
                    <GameButton
                        config={config.rewardClaimButton || config.continueButton}
                        dataConfigKey="rewardClaimButton"
                        onClick={(e) => {
                            e.stopPropagation()
                            if (onButtonClick) {
                                onButtonClick()
                            }
                        }}
                    />
                )}
            </GameContainer>
        </div>
    )
}

// Out of Lives Screen - Shows when player has no lives left
export const OutOfLivesScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer
                config={config.outOfLivesOverlay}
                dataConfigKey="outOfLivesOverlay"
                resolveAssetUrl={resolveAssetUrl}
            >
                <GameText config={config.outOfLivesTitle} dataConfigKey="outOfLivesTitle" className="mb-4" />

                <GameButton
                    config={config.outOfLivesContinueButton}
                    dataConfigKey="outOfLivesContinueButton"
                    onClick={(e) => {
                        e.stopPropagation()
                        if (onButtonClick) {
                            onButtonClick()
                        }
                    }}
                />
            </GameContainer>
        </div>
    )
}

export interface PreviewSceneProps {
    config: ReactGameConfig
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
}

export const PreviewScene: React.FC<PreviewSceneProps> = ({ config, widgetId, resolveAssetUrl }) => {
    const [selectedScreen, setSelectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)
    const initialGameScreenChecked = useRef(false)

    const currentScreen = selectedScreen ?? 'main'

    return (
        <MainGame config={config} widgetId={widgetId} isPreview={true} resolveAssetUrl={resolveAssetUrl} currentScreenId={currentScreen as any} initialGameScreenChecked={initialGameScreenChecked} />
    )
}
