import React from 'react'
import { registerNode } from '../../registry'
import type { NodeDefinition, NodeEditorBodyProps, NodeEditorPropertiesProps } from '../../types'
import type { Settings } from './node.types'
import { run } from './node.runtime'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Button } from '@repo/shared/components/ui/button'

function RewardScreenNodeBody(_: NodeEditorBodyProps) {
	return <div className="w-[260px]"></div>
}

function RewardScreenProperties({ settings, onChange }: NodeEditorPropertiesProps<Settings>) {
	return (
		<div className="space-y-2">
			<div className="space-y-1">
				<Label>Reward Set</Label>
				<Select value={settings?.rewardSetId ?? ''} onValueChange={(v) => onChange({ rewardSetId: v })}>
					<SelectTrigger>
						<SelectValue placeholder="Select reward set" /> 
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="default">Default</SelectItem>
						<SelectItem value="test">Test</SelectItem>
					</SelectContent>
				</Select>
			</div>
			<div className="pt-1">
				<Button variant="link" size="sm" className="px-0" onClick={() => {}}>
					Open in designer
				</Button>
			</div>
		</div>
	)
}

const def: NodeDefinition<Settings> = {
	type: 'client:RewardScreen',
	label: 'Game Reward Screen',
	icon: 'GiftIcon',
    inputs: () => [{ key: 'input',  acceptsOnly: ['game-outcome'] }],
	outputs: () => [{ key: 'output', kind: 'event' }],
	editor: {
		renderNodeBody: RewardScreenNodeBody,
		renderProperties: RewardScreenProperties,
		
		onInputConnected: (ctx) => {
			const endLabel = (ctx.edge?.data as any)?.endLabel
			const screenId = "outcome-" + ctx.edge.id
			ctx.customScreensContext.addCustomScreen(ctx.graph.parentWidgetId, screenId, "Outcome - " + endLabel)
			ctx.updateNodeSettings(ctx.targetNodeId, { screenId: "custom/" + screenId })
		},
	
		onInputDisconnected: (ctx) => {
			ctx.customScreensContext.removeCustomScreen(ctx.graph.parentWidgetId, "outcome-" + ctx.edge.id)
		},

	},
	runtime: { run },
}

registerNode(def)
export default def



