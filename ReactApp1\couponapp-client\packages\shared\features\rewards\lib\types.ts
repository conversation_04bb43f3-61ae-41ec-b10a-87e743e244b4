import type { AssetUrl } from '@repo/shared/lib/types/widgetSettings'

// Editor/consumer simple reward types
export type RewardType = 'coupon' | 'gift_card'

export interface CouponSettings { codes: string[] }
export interface GiftCardSettings { amount: string; currency: string }

export interface Reward {
  id: string
  name: string
  description: string
  image?: AssetUrl
  type: RewardType
  settings: CouponSettings | GiftCardSettings
  dropRate?: number
}

export type RewardSet = { id: string; rewards: Reward[] }

