{"name": "@repo/shared", "version": "0.0.0", "exports": {"./lib/*": {"types": "./lib/*.ts", "default": "./lib/*.js"}, "./lib/flow/*": {"types": "./lib/flow/*.ts", "default": "./lib/flow/*.js"}, "./components/*": {"types": "./components/*.tsx", "default": "./components/*.js"}, "./games/*": {"types": "./games/*.tsx", "default": "./games/*.js"}, "./features/*": {"types": "./features/*.ts", "default": "./features/*.js"}}, "license": "MIT", "scripts": {"lint": "eslint \"**/*.ts\""}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/react": "^18.3.1", "eslint": "^8.57.0", "typescript": "5.5.4"}, "dependencies": {"@dnd-kit/core": "^6.2.0", "@dnd-kit/modifiers": "^8.0.0", "@dnd-kit/sortable": "^9.0.0", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-query": "^5.59.16", "@tanstack/react-virtual": "^3.13.6", "@tiptap/extension-mention": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@types/dagre": "^0.7.52", "@uidotdev/usehooks": "^2.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dagre": "^0.8.5", "fuse.js": "^7.1.0", "jotai": "^2.12.0", "lucide-react": "^0.471.2", "mitt": "^3.0.1", "react": "18.3.1", "react-arborist": "^3.4.0", "react-dom": "18.3.1", "react-easy-crop": "^5.2.0", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "unique-names-generator": "^4.7.1", "usehooks-ts": "^3.1.1"}}