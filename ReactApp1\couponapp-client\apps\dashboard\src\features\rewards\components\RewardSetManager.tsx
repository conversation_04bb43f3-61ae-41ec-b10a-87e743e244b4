import React, { useMemo, useState } from 'react'
import type { RewardDefinition, RewardSet, RewardType } from '@repo/shared/features/rewards/types'
import { Button } from '@repo/shared/components/ui/button'
import { Input } from '@repo/shared/components/ui/input'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'

export interface RewardSetManagerProps {
  set: RewardSet
  onChange?: (next: RewardSet) => void
}

function genId(prefix = 'reward') {
  return `${prefix}-${Math.random().toString(36).slice(2, 8)}`
}

export const RewardSetManager: React.FC<RewardSetManagerProps> = ({ set, onChange }) => {
  const [local, setLocal] = useState<RewardSet>(set)

  // keep external in sync if parent updates the set prop
  React.useEffect(() => setLocal(set), [set])

  const rewardTypes: RewardType[] = useMemo(() => ['coupon-code', 'claimable-url'], [])

  const [newReward, setNewReward] = useState<Pick<RewardDefinition, 'name' | 'description' | 'type'>>({
    name: '',
    description: '',
    type: rewardTypes[0],
  })

  const handleRemove = (id: string) => {
    const next: RewardSet = { ...local, rewards: local.rewards.filter((r) => r.id !== id) }
    setLocal(next)
    onChange?.(next)
  }

  const handleAdd = () => {
    if (!newReward.name?.trim()) return
    const reward: RewardDefinition = {
      id: genId(),
      name: newReward.name.trim(),
      description: newReward.description?.trim() ?? '',
      type: newReward.type,
      dropRate: 1.0,
    }
    const next: RewardSet = { ...local, rewards: [...(local.rewards || []), reward] }
    setLocal(next)
    onChange?.(next)
    setNewReward({ name: '', description: '', type: rewardTypes[0] })
  }

  return (
    <div className="w-full space-y-6">
      <div className="space-y-4">
        <div className="text-lg font-medium text-foreground">Current Rewards</div>
        <div className="space-y-3">
          {local.rewards?.length ? (
            local.rewards.map((r) => (
              <div key={r.id} className="flex items-start gap-4 p-4 rounded-lg border border-border bg-card/50 hover:bg-card transition-colors">
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-foreground truncate">{r.name}</div>
                  {r.description ? (
                    <div className="text-sm text-muted-foreground mt-1 truncate">{r.description}</div>
                  ) : null}
                  <div className="text-xs text-muted-foreground mt-2 px-2 py-1 bg-accent/50 rounded-md inline-block">
                    {r.type}
                  </div>
                </div>
                <Button variant="outline" size="sm" onClick={() => handleRemove(r.id)} className="shrink-0">
                  Remove
                </Button>
              </div>
            ))
          ) : (
            <div className="flex items-center justify-center h-32 border border-dashed border-border rounded-lg">
              <div className="text-center text-muted-foreground">
                <div className="text-sm">No rewards in this set</div>
                <div className="text-xs mt-1">Add your first reward below</div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4 p-4 rounded-lg border border-border bg-card/30">
        <div className="text-lg font-medium text-foreground">Add New Reward</div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="md:col-span-2">
              <Label htmlFor="reward-name" className="text-sm font-medium text-foreground mb-2 block">
                Reward Name
              </Label>
              <Input
                id="reward-name"
                placeholder="Enter reward name"
                value={newReward.name}
                onChange={(e) => setNewReward((s) => ({ ...s, name: e.target.value }))}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="reward-type" className="text-sm font-medium text-foreground mb-2 block">
                Type
              </Label>
              <Select value={newReward.type} onValueChange={(v) => setNewReward((s) => ({ ...s, type: v as RewardType }))}>
                <SelectTrigger id="reward-type" className="w-full">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {rewardTypes.map((t) => (
                    <SelectItem key={t} value={t}>{t}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <Label htmlFor="reward-desc" className="text-sm font-medium text-foreground mb-2 block">
              Description (Optional)
            </Label>
            <Input
              id="reward-desc"
              placeholder="Enter reward description"
              value={newReward.description}
              onChange={(e) => setNewReward((s) => ({ ...s, description: e.target.value }))}
              className="w-full"
            />
          </div>
          <div className="flex justify-end">
            <Button onClick={handleAdd} disabled={!newReward.name?.trim()} className="px-6">
              Add Reward
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RewardSetManager

