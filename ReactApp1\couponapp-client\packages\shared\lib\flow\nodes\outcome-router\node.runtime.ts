import type { NodeRunResult, NodeRuntimeContext, NodeChildRef } from '../../types'
import type { Settings, Outcome, OutcomeCondition, GameResult } from './node.types'


export async function run({ ctx, settings, children }: { ctx: NodeRuntimeContext; settings: Settings;  children?: NodeChildRef[] }): Promise<NodeRunResult> {
    
    console.log('outcome-router running', settings)
    console.log('children', children)

    //array without first element.
    const withoutFallback = settings.outcomes.slice(1)
    //reversed
    const reversed = withoutFallback.reverse()

    for(const outcome of reversed) {
        for(const condition of outcome.conditions) {
            if(condition.type === 'scoreGte') {
                console.log('scoreGte', condition.value, ' , going to outcome ', outcome.id)
                console.log('children', children)
                if(ctx.payload?.score >= condition.value) {
                    console.log('found scoreGte', condition.value, ' , going to outcome ', outcome.id)
                    console.log('next ', children.find(c => c.port === `out_${outcome.id}`))
                    return { next: children.find(c => c.port === `out_${outcome.id}`) }
                }
            }
        }
    }
    
    return { next: children[0] } //fallback 
}