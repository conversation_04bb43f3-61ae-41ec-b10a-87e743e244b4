import type { RewardDefinition } from '../types'

const MOCK_REWARD_SET = {
  id: 'default-set',
  rewards: [
    {
      id: 'reward-1',
      name: '50% Off Coupon',
      description: 'Get 50% off your next purchase',
      type: 'coupon-code' as const,
      image: { absoluteUrl: 'https://placehold.co/100x100/00ff00/ffffff?text=50OFF' },
    },
  ] as RewardDefinition[],
}

export async function fetchRewardSets(): Promise<Array<{ id: string; rewards: RewardDefinition[] }>> {
  await delay(200)
  return [MOCK_REWARD_SET]
}

export async function fetchRewardFromSet(setId: string): Promise<RewardDefinition | null> {
  await delay(200)
  const set = setId ? MOCK_REWARD_SET : MOCK_REWARD_SET
  return set.rewards[0] ?? null
}

function delay(ms: number) { return new Promise((r) => setTimeout(r, ms)) }

