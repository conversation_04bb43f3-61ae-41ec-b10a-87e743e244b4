Imrpove rewards feature. Move it to vertical slices.
- Dashboard and shared should have new features/rewards folders
- dashboard edit components goes to features/rewards/components
- api, types, utils goes to features/rewards/lib
- hooks goes to features/rewards/hooks
- remover all advanced logic from rewards. No picking logic. No complex types. No outcome screens. No nothing. 
- Mock just one reward set within the api functions to fetch rewards
- Mock a reward from a reward set
- No roll history, no advanced stuff. Remove everything from old reward manager. Keep only reward sets and list of rewards. 
